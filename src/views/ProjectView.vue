<template>
  <div class="project-view">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading">
      <div class="loading-spinner"></div>
      <p>加载项目数据中...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error">
      <h2>加载失败</h2>
      <p>{{ error.message }}</p>
      <button @click="retryLoad" class="retry-btn">重试</button>
    </div>

    <!-- 正常显示 -->
    <template v-else>
      <!-- 天空盒选择器 -->
      <div class="skybox-selector" v-if="modelUrl">
        <label for="skybox-select">天空盒:</label>
        <select
          id="skybox-select"
          v-model="skyboxTextureName"
          @change="onSkyboxChange"
          class="skybox-select"
        >
          <option value="default">默认</option>
          <option value="01">天空盒 01</option>
          <option value="02">天空盒 02</option>
          <option value="03">天空盒 03</option>
          <option value="04">天空盒 04</option>
          <option value="05">天空盒 05</option>
          <option value="06">天空盒 06</option>
          <option value="07">天空盒 07</option>
          <option value="08">天空盒 08</option>
          <option value="09">天空盒 09</option>
        </select>
      </div>

      <!-- 3DGS Viewer容器 -->
      <ViewerContainer
        v-if="modelUrl"
        :model-url="modelUrl"
        :camera-config="cameraConfig"
        :anim-tracks="animTracks"
        :skybox-texture-name="skyboxTextureName"
        class="viewer-wrapper"
      />

      <!-- 无有效数据提示 -->
      <div v-else class="not-found">
        <h2>无法加载模型</h2>
        <p>未找到有效的项目ID或模型URL</p>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watchEffect } from 'vue'
import ViewerContainer from '@/core/ViewerContainer.vue'
import { SceneLoader } from '@/utils/sceneLoader'
import { getUrlParam } from '@/utils/url' // 导入获取URL参数的工具函数
import type { IParsedSceneData } from '@/types/scene'
import type { CameraConfig } from '@/types/project'

// 响应式数据
const loading = ref(false)
const error = ref<Error | null>(null)
const sceneData = ref<IParsedSceneData | null>(null)
const modelUrl = ref<string | null>(null)
const cameraConfig = ref<CameraConfig>({
  position: [10, 5, 20],
  lookAt: [0, 0, 0],
  lookUp: [0, -1, 0],
  fov: 50,
})
const animTracks = ref<any[]>([])
const skyboxTextureName = ref('default') // 天空球纹理名称

// 从URL获取天空球参数
const getSkyboxTextureName = () => {
  const stateName = getUrlParam('stateName')
  // 验证stateName是否为有效取值
  const validStates = ['01', '02', '03', '04', '05', '06', '07', '08', '09', 'default']
  return validStates.includes(stateName || '') ? stateName : 'default'
}

// 加载场景数据
const loadScene = async () => {
  loading.value = true
  error.value = null

  try {
    console.log('[ProjectView] 开始智能加载场景')

    const result = await SceneLoader.smartLoadScene()

    // 更新天空球纹理名称
    skyboxTextureName.value = getSkyboxTextureName()

    sceneData.value = result.sceneData
    modelUrl.value = result.modelUrl
    cameraConfig.value = result.cameraConfig
    animTracks.value = result.sceneData?.animTracks || []

    console.log('[ProjectView] 场景加载完成:', {
      hasSceneData: !!result.sceneData,
      modelUrl: result.modelUrl,
      cameraConfig: result.cameraConfig,
      animTracks: animTracks.value,
      skyboxTextureName: skyboxTextureName.value,
    })
  } catch (err) {
    console.error('[ProjectView] 场景加载失败:', err)
    error.value = err as Error
  } finally {
    loading.value = false
  }
}

// 监听URL参数变化，动态更新天空球
watchEffect(() => {
  const newTextureName = getSkyboxTextureName()
  if (newTextureName !== skyboxTextureName.value) {
    skyboxTextureName.value = newTextureName
    console.log(`天空球已更新为: ${newTextureName}`)
  }
})

// 天空盒变化处理
const onSkyboxChange = () => {
  console.log(`天空盒已切换为: ${skyboxTextureName.value}`)
}

// 重试加载
const retryLoad = () => {
  loadScene()
}

// 组件挂载时加载场景
onMounted(() => {
  loadScene()
})
</script>

<style scoped>
.project-view {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 加载状态 */
.loading {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading p {
  color: #666;
  font-size: 16px;
}

/* 错误状态 */
.error {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 20px;
  background: #fff5f5;
}

.error h2 {
  color: #e74c3c;
  margin: 0 0 10px 0;
}

.error p {
  color: #c0392b;
  margin: 5px 0 20px 0;
}

.retry-btn {
  padding: 10px 20px;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.3s;
}

.retry-btn:hover {
  background: #2980b9;
}

/* 项目头部 */
.project-header {
  padding: 15px 20px;
  background: rgba(0, 0, 0, 0.85);
  color: white;
  text-align: center;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  backdrop-filter: blur(5px);
}

.project-header h1 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
}

.project-header p {
  margin: 0 0 10px 0;
  font-size: 14px;
  opacity: 0.9;
}

.project-stats {
  display: flex;
  justify-content: center;
  gap: 20px;
  font-size: 12px;
  opacity: 0.8;
}

.project-stats span {
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

/* 天空盒选择器 */
.skybox-selector {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 1000;
  background: rgba(0, 0, 0, 0.8);
  padding: 12px 16px;
  border-radius: 8px;
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  gap: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.skybox-selector label {
  color: white;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
}

.skybox-select {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  color: white;
  padding: 6px 12px;
  font-size: 14px;
  min-width: 120px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.skybox-select:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

.skybox-select:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.2);
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.3);
}

.skybox-select option {
  background: #2c3e50;
  color: white;
  padding: 8px;
}

/* Viewer容器 */
.viewer-wrapper {
  flex: 1;
}

/* 未找到状态 */
.not-found {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 20px;
  background: #f5f5f5;
}

.not-found h2 {
  color: #333;
  margin: 0 0 15px 0;
}

.not-found p {
  color: #666;
  margin: 8px 0;
  line-height: 1.5;
}
</style>
